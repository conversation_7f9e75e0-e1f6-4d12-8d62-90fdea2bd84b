// ✅ Gradle Plugins Section
plugins {
    id("com.android.application")
    id("kotlin-android")
    id("com.google.gms.google-services") // ✅ Added: Google Services plugin
    id("dev.flutter.flutter-gradle-plugin") // Must be last
}

android {
    namespace = "com.example.quipgen_eleaning"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        applicationId = "com.example.quipgen_eleaning"
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}

flutter {
    source = "../.."
}

// ✅ Dependencies block (Add Firebase Auth SDKs)
dependencies {
    implementation("com.google.firebase:firebase-auth-ktx:22.3.1") // ✅ Added Firebase Auth
    implementation("com.google.firebase:firebase-analytics-ktx:21.5.0") // ✅ Optional but useful
}
