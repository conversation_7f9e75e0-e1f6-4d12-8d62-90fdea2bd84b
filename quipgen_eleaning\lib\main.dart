import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:quipgen_eleaning/firebase_options.dart';
import 'package:quipgen_eleaning/view/auth/auth_wrapper.dart';
import 'package:quipgen_eleaning/view/home/<USER>';
import 'package:quipgen_eleaning/view/onboarding/onboarding_screen.dart';
import 'package:quipgen_eleaning/view/login/signup_screen.dart';
import 'package:quipgen_eleaning/view/login/login_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      debugShowCheckedModeBanner: false,
      initialRoute: '/',
      routes: {
        '/': (context) => const AuthWrapper(),
        '/onboarding': (context) => const OnboardingScreen(),
        '/home': (context) => const HomeScreen(),
        '/signup': (context) => const SignupScreen(),
        '/login': (context) => const LoginScreen(),
      },
    );
  }
}
