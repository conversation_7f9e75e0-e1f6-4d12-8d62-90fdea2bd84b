{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "C:/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-891b0f485eae6e3b997a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-ca0ffc67f408927a91eb.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-7e50f3fe2415bdc038b9.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-ca0ffc67f408927a91eb.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-7e50f3fe2415bdc038b9.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-891b0f485eae6e3b997a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}